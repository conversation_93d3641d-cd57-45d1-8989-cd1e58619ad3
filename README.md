# 项目介绍

# <p align="center">小程序管理员后台</p>
<p align="center">2025我们一起加油，心如所愿，梦想成真.</p>
<p align="center"><img src="./img/1.png"></p>



**相关项目**：

- 小程序前端请前往：https://github.com/no1xuan/photo
- 小程序后端请前往：https://github.com/no1xuan/HivisionIDPhotos-wechat-weapp


------

# ⭐最近更新
- 2024.END：  2024年最后一个版本，后台功能增强 
- 2024.12.17：后台功能增强
- 2024.10.23：修复首页判断错误导致无token来回乱跳页面问题，修复部分浏览器页面中文乱码问题
- 2024.10.22：修复首页单词拼写错误而导致的记忆登录没生效
- 2024.10.07：诞生

<hr>
<br>

# 🔧部署

打开statics/web.js，修改一个地址就好啦
<br>
然后上传到服务器部署静态网站就一切OK啦

<img src="./img/2.png">

<hr>
<br>

# ⚡️注意事项
1. 建议小程序发布到线上后再部署管理员网页后台。
如果你想本地测试，就打开JAVA后端的application.yml，然后按需操作修改
2. 默认使用小程序第一个注册（id=1）的当作管理员，其它用户均无法登录管理员后台，请熟知
3. 本地打开文件乱码问题？ 答：放服务器部署就不乱码了，如果你非要本地弄，就把user/里面所有文件用记事本打开，然后另存为，选择编码修改成UTF-8，确定覆盖保存
<img src="./img/3.png">

<hr>
<br>

# 📚引用

管理员后台模板借鉴了【郑州市鹧应网络科技有限责任公司】旗下的鹧应证件照：https://zjzapi.com
<br>
如侵犯到贵公司，十分抱歉，请与我联系，我将删除源代码

<hr>
<br>

## 📧其它

您可以通过以下方式联系我:

QQ: 24677102

微信：webxuan
