/** layui-v2.5.0 MIT License By https://www.layui.com */
 ;layui.define("form",function(a){"use strict";var e=layui.form,i=layui.$,t={config:{},index:layui.transfer?layui.transfer.index+1e4:0,set:function(a){var e=this;return e.config=i.extend({},e.config,a),e},on:function(a,e){return layui.onevent.call(this,l,a,e)}},n=function(){var a=this,e=a.config;return{getValue:function(){return a.getValue()},config:e}},l="transfer",r="layui-transfer",d="layui-transfer-left",s="layui-transfer-right",c="layui-transfer-dataLeft",f="layui-transfer-dataRight",u="layui-transfer-btnLeft",h="layui-transfer-btnRight",o="layui-btn-disabled",y=function(a){var e=this;e.index=++t.index,e.config=i.extend({},e.config,t.config,a),e.render()};y.prototype.config={title:["列表一","列表二"],data:[],value:[],showSearch:!1,id:""},y.prototype.render=function(){var a=this,t=a.config,n="",l="",d=[];"function"==typeof t.parseData&&layui.each(t.data,function(a,e){t.data[a]=t.parseData(e)||e});var s=0,c="",f=0,u=0;layui.each(t.data,function(a,e){s=0,layui.each(t.value,function(a,i){if(e.value==i)return s=1,!0}),c='<li data-title="'+e.title+'"><input lay-skin="primary" type="checkbox" data-index="'+a+'" name="'+(0==s?"layTranLeftCheck":"layTranRightCheck")+'" title="'+e.title+'" value="'+e.value+'" class="layui-input" '+(e.disabled?"disabled":"")+"></li>",0==s?(n+=c,f+=e.disabled?0:1):(l+=c,u+=e.disabled?0:1,d.push(a))});var h=['<div class="layui-transfer layui-form" id="transfer-'+t.id+'" lay-filter="LAY-Transfer-'+a.index+'">','<div class="layui-transfer-left" data-total="'+f+'">','<div class="layui-transfer-topTitle"><input lay-skin="primary" name="layTranLeftCheck" lay-filter="layTranLeftCheckAll" type="checkbox" class="layui-input" title="'+t.title[0]+'"></div>',function(){return t.showSearch?'<div class="layui-transfer-search"><input class="layui-input" placeholder="关键字搜索"><i class="layui-icon layui-icon-search layui-transfer-searchI"></i></div><ul class="layui-transfer-data layui-transfer-dataLeft short">':'<ul class="layui-transfer-data layui-transfer-dataLeft">'}(),n+"</ul>","</div>",'<div class="layui-transfer-btn">','<button class="layui-btn layui-btn-primary layui-transfer-btnRight layui-btn-disabled"><i class="layui-icon layui-icon-next"></i></button>','<button class="layui-btn layui-btn-primary layui-transfer-btnLeft layui-btn-disabled"><i class="layui-icon layui-icon-prev"></i></button></div>','<div class="layui-transfer-right" data-arr="'+d+'" data-total="'+u+'">','<div class="layui-transfer-topTitle"><input lay-skin="primary" name="layTranRightCheck" lay-filter="layTranRightCheckAll" type="checkbox" class="layui-input" title="'+t.title[1]+'"></div>',function(){return t.showSearch?'<div class="layui-transfer-search"><input class="layui-input" placeholder="关键字搜索"><i class="layui-icon layui-icon-search layui-transfer-searchI"></i></div><ul class="layui-transfer-data layui-transfer-dataRight short">':'<ul class="layui-transfer-data layui-transfer-dataRight">'}(),l+"</ul>","</div>","</div>"].join(""),o=i(t.elem),y=o.next("."+r);y[0]&&y.remove(),a.elemTemp=i(h),o.html(a.elemTemp),a.event(),e.render("checkbox","LAY-Transfer-"+a.index)},y.prototype.event=function(){function a(a){var e=l.find(a).find(".layui-transfer-search"),t=e.children("input").val(),n=e.next();n.children("li").each(function(){i(this).data("title").indexOf(t)==-1?i(this).hide():i(this).show()})}var t=this,n=t.config,l=t.elemTemp,r="",y="",p=l.find("."+d).find(".layui-transfer-topTitle").find('input[name="layTranLeftCheck"]'),v=l.find("."+s).find(".layui-transfer-topTitle").find('input[name="layTranRightCheck"]'),k=l.find("."+d).data("total"),b=l.find("."+s).data("total");l.on("click",'input[name="layTranLeftCheck"]+',function(){var a=i(this).prev(),n=a[0].checked,d=l.find("."+c).find('input[name="layTranLeftCheck"]'),s="layTranLeftCheckAll"==a.attr("lay-filter");if(!a[0].disabled){if(s)r="",0==k?a[0].checked=!1:(d.each(function(a,e){e.disabled||(e.checked=n,n?(r+=i(d[a]).data("index")+",",i(d[a]).parent("li").addClass("selected")):i(d[a]).parent("li").removeClass("selected"))}),n?l.find("."+h).removeClass(o):l.find("."+h).addClass(o));else{var f=1;n?(d.each(function(a,e){e.disabled||e.checked||(f=0)}),1==f&&(p[0].checked=n),r+=a.data("index")+",",a.parent("li").addClass("selected"),l.find("."+h).removeClass(o)):(p[0].checked&&(p[0].checked=!1),a.parent("li").removeClass("selected"),r=r.replace(a.data("index"),""),d.each(function(a,e){e.checked&&(f=0)}),1==f&&l.find("."+h).addClass(o))}e.render("checkbox","LAY-Transfer-"+t.index)}}),l.on("click",'input[name="layTranRightCheck"]+',function(){var a=i(this).prev(),n=a[0].checked,r=l.find("."+f).find('input[name="layTranRightCheck"]'),d="layTranRightCheckAll"===a.attr("lay-filter");if(!a[0].disabled){if(d)y="",0==b?a[0].checked=!1:(r.each(function(a,e){e.disabled||(e.checked=n,n?(y+=i(r[a]).data("index")+",",i(r[a]).parent("li").addClass("selected")):i(r[a]).parent("li").removeClass("selected"))}),n?l.find("."+u).removeClass(o):l.find("."+u).addClass(o)),e.render("checkbox","LAY-Transfer-"+t.index);else{var s=1;n?(r.each(function(a,e){e.disabled||e.checked||(s=0)}),1==s&&(v[0].checked=n),y+=a.data("index")+",",a.parent("li").addClass("selected"),l.find("."+u).removeClass(o)):(v[0].checked&&(v[0].checked=!1),a.parent("li").removeClass("selected"),y=y.replace(a.data("index"),""),r.each(function(a,e){e.checked&&(s=0)}),1==s&&l.find("."+u).addClass(o))}e.render("checkbox","LAY-Transfer-"+t.index)}}),l.on("click","."+h,function(){var d="";if(!i(this).hasClass(o)){l.find("."+c).find("li").each(function(){i(this).hasClass("selected")&&i(this).remove()});var u=l.find("."+s).data("arr"),h=0;layui.each(r.split(","),function(a,e){if(e){var i=n.data[e];d+='<li data-title="'+i.title+'"><input lay-skin="primary" type="checkbox" data-index="'+e+'" name="layTranRightCheck" title="'+i.title+'" value="'+i.value+'" class="layui-input" '+(i.disabled?"disabled":"")+"></li>",u+=","+e,h++}}),l.find("."+f).append(d),a("."+s),p[0].checked=!1,v[0].checked=!1,i(this).addClass(o),r="",b+=h,k-=h,l.find("."+s).data("total",b),l.find("."+s).data("total",k),l.find("."+s).data("arr",u),e.render("checkbox","LAY-Transfer-"+t.index),n.onchange&&n.onchange(t.getValue())}}),l.on("click","."+u,function(){var r="";if(!i(this).hasClass(o)){l.find("."+f).find("li").each(function(){i(this).hasClass("selected")&&i(this).remove()});var u=l.find("."+s).data("arr"),h=0;layui.each(y.split(","),function(a,e){if(e){var i=n.data[e];r+='<li data-title="'+i.title+'"><input lay-skin="primary" type="checkbox" data-index="'+e+'" name="layTranLeftCheck" title="'+i.title+'" value="'+i.value+'" class="layui-input" '+(i.disabled?"disabled":"")+"></li>",u=u.replace(e,"").replace(/(,)+/g,","),h++}}),l.find("."+c).append(r),a("."+d),p[0].checked=!1,v[0].checked=!1,i(this).addClass(o),y="",b-=h,k+=h,l.find("."+s).data("total",b),l.find("."+s).data("total",k),l.find("."+s).data("arr",u),e.render("checkbox","LAY-Transfer-"+t.index),n.onchange&&n.onchange(t.getValue())}}),l.find(".layui-transfer-search").on("keyup","input",function(){var e=i(this).parent("div").parent("div");a(e)})},y.prototype.getValue=function(){function a(a,e){return a-e}var e=this,i=e.config,t=e.elemTemp.find("."+s).data("arr"),n=[];return layui.each(t.split(",").sort(a),function(a,e){e&&n.push(i.data[e])}),n},t.render=function(a){var e=new y(a);return n.call(e)},a(l,t)});